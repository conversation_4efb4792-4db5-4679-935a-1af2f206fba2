import { lazy } from 'ice';

import { activities, previewActivities } from '@/routers/activity';
import commonReportData from './common.router';
import apple from './apple.router';
import aptamil from './aptamil.router';
import decorate from './decorate.router';
import feihe from './feihe.router';
import feihePop from './feihePop.router';
import huggies from './huggies.router';
import langjiu from './langjiu.router';
import mengniu from './mengniu.router';
import proya from './proya.router';
import smartCardRouter from './smartCard.routers';
import yili from './yili.router';
import heshengyuan from '@/routers/heshengyuan.router';
import mengniuNiudan from './mengniuNiudan.router';
import swisse from './swisse.router';
import sanyuan from './sanyuan.router';
import legendSandy from './legendSandy.router';
import vivo from './vivo.router';
import pg from './pg.router';
import pechoin from './pechoin.router';
import babyCare from './babyCare.router';
import oppo from './oppo.router';
import lamer from './lamer.router';
import kabrita from './kabrita.router';
import nestle from './nestle.router';
import caltrate from './caltrate.router';
import snapdragon from './snapdragon.router';
import propertyShoppingCard from './property.router';

const Home = lazy(() => import('@/pages/home'));

export const routers = [
  {
    name: '首页',
    path: '/',
    exact: true,
    component: Home,
  },
  ...propertyShoppingCard,
  ...nestle,
  ...caltrate,
  ...aptamil,
  ...activities,
  ...previewActivities,
  ...commonReportData,
  ...heshengyuan,
  ...pg,
  ...activities,
  ...previewActivities,
  ...smartCardRouter,
  ...decorate,
  ...feihe,
  ...langjiu,
  ...proya,
  ...mengniu,
  ...huggies,
  ...yili,
  ...feihePop,
  ...apple,
  ...mengniuNiudan,
  ...swisse,
  ...sanyuan,
  ...legendSandy,
  ...pechoin,
  ...babyCare,
  ...vivo,
  ...oppo,
  ...lamer,
  ...kabrita,
  ...snapdragon,
];

export default routers;

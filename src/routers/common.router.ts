import { lazy } from 'ice';

const CustomizedActivityReport = lazy(() => import('@/pages/common/report'));
const CustomizedManage = lazy(() => import('@/pages/common/customized'));
const CustomizedSelect = lazy(() => import('@/pages/common/select'));
const CustomizedActivitySquare = lazy(() => import('@/pages/common/square'));
const CustomizedActivityList = lazy(() => import('@/pages/common/list'));

export default [
  {
    name: '定制互动广场',
    path: '/square',
    exact: true,
    component: CustomizedActivitySquare,
  },
  {
    name: '定制活动列表',
    path: '/activity',
    exact: true,
    component: CustomizedActivityList,
  },
  {
    name: '定制活动模板选择页',
    path: '/select',
    exact: true,
    component: CustomizedSelect,
  },
  {
    name: '纯C定制活动列表',
    path: '/customized',
    exact: true,
    component: CustomizedManage,
  },
  {
    name: '通用报表查询',
    path: '/activity/report',
    exact: true,
    component: CustomizedActivityReport,
  },
];

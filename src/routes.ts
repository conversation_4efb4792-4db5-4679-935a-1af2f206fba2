import { isInIcestark } from '@ice/stark-app';
import BasicLayout from '@/layouts/BasicLayout';
import InIcestarkLayout from '@/layouts/InIcestarkLayout';
import routers from '@/routers/index.router';
import { APP_MODE, lazy } from 'ice';
import { RouteProps } from 'react-router';

const isDev = APP_MODE === 'dev' || APP_MODE === 'local';

const Login = lazy(() => import('@/pages/login'));

// 在子系统里面的时候路由
const InIcestarkRouters = [
  {
    component: InIcestarkLayout,
    children: routers,
  },
];

// 独立运行的时候路由
const NotInIcestarkRouters: RouteProps[] = [
  {
    path: '/',
    component: BasicLayout,
    children: [...routers],
  },
];

if (isDev) {
  NotInIcestarkRouters.unshift({
    path: '/login',
    exact: true,
    component: Login,
  });
}

const routerConfig = isInIcestark() ? InIcestarkRouters : NotInIcestarkRouters;

export default routerConfig;

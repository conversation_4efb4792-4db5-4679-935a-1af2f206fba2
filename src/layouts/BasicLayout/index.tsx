import routers from '@/routers/index.router';
import constant from '@/utils/constant';
import { Nav, Search, Shell } from '@alifd/next';
import React, { useEffect } from 'react';
import styles from './index.module.scss';

function groupByPathPrefix(routes: any[]) {
  const groups: Record<string, any[]> = {};
  routes.forEach((route) => {
    // 判断是否属于首页分组
    if (
      route.path === '/' ||
      route.path.startsWith('/activity') ||
      route.path.startsWith('/square') ||
      route.path.startsWith('/customized') ||
      route.path.startsWith('/activity/report') ||
      route.path.startsWith('/select')
    ) {
      if (!groups['首页']) groups['首页'] = [];
      groups['首页'].push(route);
    } else {
      // 取 path 的第一级作为分组名
      const match = route.path.match(/^\/([^/]+)/);
      const group = match ? match[1] : '其他';
      if (!groups[group]) groups[group] = [];
      groups[group].push(route);
    }
  });
  return groups;
}

export default function InIcestarkLayout({ history, children }) {
  const [key, setKey] = React.useState<string>(history.location.pathname);
  const [searchValue, setSearchValue] = React.useState<string>('');

  const navs = React.useMemo(
    () => routers.filter((route: any) => route.name && route.path && !route.hideInMenu),
    [routers],
  );

  const filteredNavs = React.useMemo(() => {
    const result = searchValue
      ? navs.filter((route: any) => route.name.includes(searchValue) || route.path.includes(searchValue))
      : navs;
    return result.slice().sort((a: any, b: any) => a.path.localeCompare(b.path));
  }, [navs, searchValue]);

  // 分组
  const groupedNavs = React.useMemo(() => groupByPathPrefix(filteredNavs), [filteredNavs]);

  const onItemClick = (key: string) => {
    history.push(key);
    setKey(key);
  };

  const handleLogout = () => {
    localStorage.removeItem(constant.LZ_SSO_TOKEN);
    history.replace(constant.LZ_LOGIN_PAGE_URL);
  };

  return (
    <>
      <Shell className={styles.iframeHack} type="brand" style={{ border: '1px solid #eee' }}>
        <Shell.Branding>
          <span style={{ marginLeft: 10 }}>互动子系统</span>
        </Shell.Branding>
        <Shell.Navigation direction="hoz">
          <Search
            key="2"
            shape="simple"
            placeholder="Search"
            style={{ width: '200px' }}
            value={searchValue}
            onChange={setSearchValue}
            onSearch={setSearchValue}
          />
        </Shell.Navigation>
        <Shell.Action>
          <span style={{ marginLeft: 10, cursor: 'pointer' }} onClick={handleLogout}>
            退出登录
          </span>
        </Shell.Action>
        <Shell.Navigation style={{ width: '280px', overflowY: 'scroll' }}>
          <Nav embeddable onItemClick={onItemClick} selectedKeys={[key]}>
            {Object.entries(groupedNavs).map(([group, items]) => (
              <Nav.Group key={group} label={group}>
                {(items as any[]).map((route) => (
                  <Nav.Item icon="form" key={route.path} title={route.path}>
                    {route.name}
                  </Nav.Item>
                ))}
              </Nav.Group>
            ))}
          </Nav>
        </Shell.Navigation>
        <Shell.Content className={styles.iframeHackContent}>
          <div className="crm-interaction-container">{children}</div>
        </Shell.Content>
      </Shell>
    </>
  );
}

import { Icon } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

export default (props) => {
  const { buttons, onClick } = props;
  return (
    <div style={{ display: 'flex', columnGap: 5 }}>
      {buttons &&
        buttons.map((btn) => (
          <div key={btn.key} onClick={() => onClick(btn)} className={[styles.LzButtonBtn, styles[btn.key]].join(' ')}>
            {btn.icon && <Icon type={btn.icon} style={{ marginRight: 5 }} />}
            {btn.label}
          </div>
        ))}
    </div>
  );
};

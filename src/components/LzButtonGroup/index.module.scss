.LzButtonBtn {
  font-size: 12px;
  position: relative;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  text-transform: none;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  transition: all 0.2s ease;
  line-height: 1;
  cursor: pointer;
}

.view {
  color: #007bff;
  &:hover { color: #0056b3; }
}

.data {
  color: #28a745;
  &:hover { color: #1e7e34; }
}

.edit {
  color: #ffc107;
  &:hover { color: #d39e00; }
}

.copy {
  color: #6c757d;
  &:hover { color: #5a6268; }
}

.record {
  color: #17a2b8;
  &:hover { color: #117a8b; }
}

.analyze {
  color: #6610f2;
  &:hover { color: #520dc2; }
}

.end,
.delete,
.stop {
  color: #dc3545;
  &:hover { color: #bd2130; }
}

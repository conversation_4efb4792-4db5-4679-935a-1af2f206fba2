$px-scale: 20;
$ui-scale: 1.5;
.preview {
  width: 100%;
  position: relative;
  text-align: center;
  background-size: cover;
  border-radius: 8.5px;
  overflow: hidden;

  .module-content {
    width: 100%;

    &.hide {
      display: none;
    }
  }

  .zr-content {
  }

  .title {
    height: 22px;
    margin-top: 0.2px * $px-scale;
    margin-bottom: 0.4px * $px-scale;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: auto 100%;
  }

  .tabs {
    display: flex;

    .tab-item {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 4.65px * $px-scale;
      height: 1.3px * $px-scale;
      padding-bottom: 0.2px * $px-scale;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;

      &.active {
        background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/248310/17/880/3375/658bca02Fd81b45bd/37ee25693e316232.png);
      }

      &.not-active {
        background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/249905/22/800/4969/658bca02Fdccbff5f/31a630b3dbba5a99.png);
      }
    }

    .tab-title {
      color: #360200;
      font-size: 11px;
    }
  }

  .tab-content {
    overflow-x: scroll;
    display: flex;
    // justify-content: space-between;
    // box-sizing: border-box;
    // width: 100%;
    padding: 0.1px * $px-scale 0.2px * $px-scale 0 0;
    gap: 10px;

    .img {
      flex-shrink: 0;
      width: 179px * 0.96;
      height: 175px * 0.96;
      position: relative;
      overflow: hidden;
    }
  }

  .tab-content::-webkit-scrollbar {
    display: none;
  }

  .swiper-container {
    width: 100%;
    height: 153px * 0.97;
  }

  .swiper {
    width: 100%;
    height: 153px * 0.97;
  }

  .swiper-img {
    width: 243px * 0.97;
    height: 153px * 0.97;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
}

.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 15px;
  background: #f4f6f9;
  border-radius: 5px;

  .imgUpload {
    display: flex;
    align-items: center;

    .tip {
      margin-left: 10px;
    }
  }

  .colorPicker {
    display: flex;
    margin-top: 10px;
    gap: 20px;

    span {
      font-weight: bold;
      margin-right: 10px;
    }
  }

  .exchangeContainer {
    background: #f4f6f9;
    border-radius: 5px;

    .imgUpload {
      display: flex;
      align-items: center;

      .tip {
        margin-left: 10px;
      }
    }
  }

  .itemContainer {
    display: flex;

    &:first-child {
      margin-top: 15px;
    }

    .itemImg {
      margin-right: 10px;
    }

    .itemInfo {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .icons {
        .transform180 {
          display: inline-block;
          transform: rotate(180deg);
        }

        i {
          margin-right: 5px;
          cursor: pointer;
          transform: rotate(180deg);
        }
      }
    }
  }
}

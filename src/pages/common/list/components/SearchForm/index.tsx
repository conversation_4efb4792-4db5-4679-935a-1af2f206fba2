import React, { useEffect, useState } from 'react';
import { DatePicker2, Field, Form, Input, Select } from '@alifd/next';
import { getActivityTypeEnum } from '@/api/common';
import { ActivityTypeResponse } from '@/api/types';
import { withRouter } from 'react-router-dom';
import dayjs from 'dayjs';
import store from '@/store';
import { getParams } from '@/utils';
import { initData } from '@/models/customizedPager';

const quarterOfYear = require('dayjs/plugin/quarterOfYear');

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
dayjs.extend(quarterOfYear);

const rangePreset = {
  上个月: [dayjs().add(-1, 'month').startOf('month'), dayjs().add(-1, 'month').endOf('month')],
  本月: [dayjs().startOf('month'), dayjs().endOf('month')],
  下个月: [dayjs().add(+1, 'month').startOf('month'), dayjs().add(+1, 'month').endOf('month')],
  昨天: [dayjs().add(-1, 'day'), dayjs()],
  今天: [dayjs(), dayjs().add(1, 'day')],
  明天: [dayjs().add(1, 'day'), dayjs().add(2, 'day')],
  // @ts-ignore
  上季度: [dayjs().subtract(1, 'quarter').startOf('quarter'), dayjs().subtract(1, 'quarter').endOf('quarter')],
  // @ts-ignore
  本季度: [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  // @ts-ignore
  下季度: [dayjs().add(1, 'quarter').startOf('quarter'), dayjs().add(1, 'quarter').endOf('quarter')],
};

const SearchForm = (props) => {
  const [_, bsdPagerAction] = store.useModel('bsdPager');
  const bsdPager = JSON.parse(getParams('params')) || initData;
  const [actTypes, setActTypes] = useState<any[]>([]);

  const field = Field.useField({
    values: { activityType: null },
  });

  const loadActTypeEmus = async () => {
    const result: ActivityTypeResponse[] = await getActivityTypeEnum();
    setActTypes(result);
  };

  const itemRender = (item) => {
    const { value, label } = item;
    return (
      <>
        <span>{label}</span>
        <span style={{ float: 'right' }}>{value}</span>
      </>
    );
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue = field.getValues();
    if (props.onSearch && typeof props.onSearch === 'function') {
      props.onSearch(formValue);
      bsdPagerAction.setState({
        ...formValue,
      });

      console.log(bsdPager, 'bsdPager');
    }
  };

  useEffect(() => {
    loadActTypeEmus().then();
  }, []);

  useEffect(() => {
    field.setValue('activityName', bsdPager?.activityName ?? '');
    field.setValue('dateRange', bsdPager?.dateRange ?? '');
    field.setValue('activityType', bsdPager?.activityType ?? []);
  }, [props.location.search]);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="activityName" label="活动名称/ID" requiredMessage="请输入活动名称/ID">
          <Input maxLength={50} placeholder="请输入活动名称或活动ID进行检索" />
        </FormItem>
        <FormItem name="activityType" label="活动类型" requiredMessage="请选择活动类型">
          <Select
            mode="multiple"
            showSearch
            hasClear
            followTrigger
            style={{ marginRight: 8 }}
            dataSource={actTypes}
            itemRender={itemRender}
          />
        </FormItem>
        <FormItem name="dateRange" label="活动时间">
          <RangePicker format="YYYY-MM-DD" preset={rangePreset} hasClear={false} />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            onClick={() => {
              const formValue = field.getValues();
              bsdPagerAction.setState({
                ...formValue,
              });
              props.onSearch(formValue);
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
    </div>
  );
};

export default withRouter(SearchForm);

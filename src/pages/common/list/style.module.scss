.activityList {
  position: relative;

  .previewIframe {
    border: none;
    width: 100%;
    height: 100%;
  }

  .timeCell {
    position: relative;
    //width: calc(100% + 32px);

    &:after {
      content: '1';
      position: absolute;
      color: transparent;
      height: 1px;
      bottom: -21px;
      background: var(--color);
      margin-left: -16px;
      width: var(--progress-length);
    }
  }

  .toOld {
    position: absolute;
    top: -10px;
    right: 0;
  }
}

.coverImg {
  height: 42px;
  cursor: pointer;
  object-fit: cover;
  width: 28px;
  margin: -10px 0 -15px 0;
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;

  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
      margin-right: 5px;
    }

    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

.syncedContainer {
  display: flex;
  justify-content: center;
  align-items: center;

  .syncedItem {
    text-align: center;

    img {
      width: 100px;
      height: 100px;
    }
  }
}

.inARow {
  display: flex;

  .copy {
    margin-left: 8px;
    font-size: 10px;
    cursor: pointer;

    &:before {
      vertical-align: sub;
    }
  }
}


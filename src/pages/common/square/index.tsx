import React, { useCallback, useEffect, useState } from 'react';
import styles from './style.module.scss';
import ResponsiveList from './components/ResponsiveList';
import { getActivitySquare } from '@/api/common';
import { CustomizedActivityTypeVo } from '@/api/types';
import { Loading, Search } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { debounce } from 'lodash';

export default () => {
  const [list, setList] = useState<CustomizedActivityTypeVo[] | undefined>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');

  const getSysApiLogList = async () => {
    try {
      setLoading(true);
      const result: CustomizedActivityTypeVo[] = await getActivitySquare();
      setList(result);
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect((): void => {
    getSysApiLogList().then();
  }, []);

  // 使用防抖处理搜索输入
  const debouncedSetSearchValue = useCallback(
    debounce((value: string) => {
      setSearchValue(value);
    }, 300),
    [],
  );

  const filterListBySearch = (items: CustomizedActivityTypeVo[] | undefined) => {
    return items?.filter((item) => {
      if (!searchValue) return true;
      const searchTerm = searchValue.toLocaleUpperCase();
      // 移除对不存在的description属性的引用
      return item.id?.toString()?.includes(searchTerm) || item.title?.toLocaleUpperCase().includes(searchTerm);
    });
  };

  return (
    <div className={styles.SquarePage}>
      <LzPanel title="定制活动广场" subTitle="基于云鹿的专属B+C定制活动广场">
        <Search
          type="secondary"
          size="medium"
          onSearch={getSysApiLogList}
          className={styles.searchInput}
          searchText="检索"
          hasClear
          defaultValue={searchValue}
          onChange={debouncedSetSearchValue}
        />
        <br />
        <Loading visible={loading} inline={false}>
          <ResponsiveList list={filterListBySearch(list)} isLoading={loading} />
        </Loading>
      </LzPanel>
    </div>
  );
};

@import "@/assets/style/variables";
@import "@/assets/style/mixin";

$padding: 10px;

.SquarePage {
  margin: 0-$padding;
}

.ActivityTypeCard {
  margin: $padding;
  box-shadow: none;
  border: solid 1px rgba(0, 0, 0, .1);
  position: relative;
  overflow: hidden;
}

.ActivityTypeCardCover {
  height: 120px;
  object-position: top;
  cursor: pointer;
}

.ActivityTypeCardTitle {
  display: flex;
  align-items: center;

  .ActivityTypeCardTitleText {
    @include lz-multi-ellipsis--l1;
    font-weight: bold;
    flex: 1;
  }
}

.ActivityTypeExp {
  font-size: smaller;
}

.ActivityTypeContent {
  line-height: 2;
  font-size: smaller;
}

.ImgInfo {
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 300ms ease;

  .ShowImgInfo {
    height: 85%;
    max-width: 90%;
    object-fit: contain;
  }

  .closeBtn {
    position: absolute;
    bottom: 10px;
    left: 50%;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
  }
}

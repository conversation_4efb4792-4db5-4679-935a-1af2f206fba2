/**
 * 日历签到数据报表
 */
import React, { useEffect, useState } from 'react';
import { Message, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import ActReportData from './components/ActReportData';
import { dataSheet } from '@/api/common';
import { getParams } from '@/utils';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  const [tabList, setTabList] = useState([]) as ISheet[][];

  interface ISheet {
    sheetName: string;
    sheetNum: number;
  }

  const loadSheetData = (): void => {
    const p = { activityMainId: getParams('activityMainId') };
    dataSheet({ ...p })
      .then((res: any): void => {
        setTabList(res);
        console.log(res);
      })
      .catch((e) => {
        Message.error(e.message);
      });
  };

  useEffect(() => {
    loadSheetData();
  }, []);

  return (
    <div className="crm-container">
      <LzPanel title="">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          {tabList.length > 0 &&
            tabList.map((item, index) => (
              <Tab.Item title={item.sheetName} key={item.sheetNum}>
                <ActReportData sheetNum={item.sheetNum} />
              </Tab.Item>
            ))}
        </Tab>
      </LzPanel>
    </div>
  );
};

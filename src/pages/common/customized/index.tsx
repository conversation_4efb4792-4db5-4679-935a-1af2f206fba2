import React, { useEffect, useReducer, useState } from 'react';
import { Balloon, Button, Dialog, Field, Form, Input, Message, Tab, Table, Typography } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import LzTag from '@/components/LzTag';
import { getActivityPageListForC } from '@/api/common';
import { IPageCustomizedActivityPageResponse } from '@/api/types';
import LzDialog from '@/components/LzDialog';
import LzPanel from '@/components/LzPanel';
import styles from './style.module.scss';
import Utils from '@/utils';
import dayjs from 'dayjs';
import constants from '@/utils/constant';
import LzPagination, { Pager } from '@/components/LzPagination';

import { appHistory } from '@ice/stark-app';
import QS from 'query-string';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;

interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

interface File {
  fileName: string;
  fileUrl: string;
}

interface List {
  name: string;
  activityId: string;
  activityImg: string;
  createTime: string;
  startTime: string;
  endTime: string;
  activityStatus: string;
  activityRule: string;
  fileList: File[];
  realmName: string;
  activityUrl: string;
  pictureLink: string;
}

const copyText = async (text: string) => {
  await Utils.copyText(text);
  Message.success('活动ID已经复制到剪切板');
};
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<List[]>([]);
  const [tabKey, setTabKey] = useState('1');
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  // 请求数据
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    activityName: '',
    activityId: '',
    ...initPager,
  });
  // 详情弹窗
  const [detailVisible, setDetailVisible] = useState(false);
  const [testVisible, setTestVisible] = useState(false);
  const [testRecord, setTestRecord] = useState<any>({});
  const [detailData, setDetailData] = useState<List>({
    name: '',
    activityId: '',
    activityImg: '',
    createTime: '',
    startTime: '',
    endTime: '',
    activityStatus: '',
    activityRule: '',
    fileList: [],
    realmName: '',
    activityUrl: '',
    pictureLink: '',
  });
  const parsePointResListStatus = (status: number) => {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '进行中';
      case 2:
        return '已结束';
      default:
        return '--';
    }
  };
  const previewImg = (title, url) => {
    Dialog.show({
      title,
      content: <img style={{ width: '750px' }} src={url} alt="" className={styles.previewImg} />,
      footer: false,
    });
  };

  const showDetail = (index) => {
    setDetailData(list[index]);
    setDetailVisible(true);
  };
  const changeTabKey = (key) => {
    setTabKey(key);
    setParams({ activityName: '', activityId: '', pageNum: 1, pageSize: params.pageSize });
  };

  const handleSubmit = () => {
    // const formValue = field.getValues() as any;
    // // formValue中undefined的值转成空字符串
    // if (formValue) {
    //   Object.keys(formValue).forEach((key) => {
    //     formValue[key] = formValue[key] || '';
    //   });
    // }
    setParams({ ...params, pageNum: 1, pageSize: params.pageSize });
    setPageInfo({ ...pageInfo, pageNum: 1 });
    getList({ ...params }).then();
  };
  const handleReset = () => {
    setParams({ activityName: '', activityId: '', pageNum: 1, pageSize: params.pageSize });
    getList({ activityName: '', activityId: '', pageNum: 1, pageSize: params.pageSize }).then();
  };
  const getList = async (query) => {
    const postData = {
      ...params,
      type: tabKey,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      ...query,
    };
    try {
      setLoading(true);
      const result: IPageCustomizedActivityPageResponse = await getActivityPageListForC(postData);
      setList((result.records as any) || []);
      setPageInfo({
        total: result.total,
        pageNum: result.current,
        pageSize: result.size,
      } as any);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPageInfo({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    getList({ ...formValue, pageSize, pageNum }).then();
  };
  useEffect(() => {
    setList([]);
    getList({ ...pageInfo, pageNum: 1 }).then();
  }, [tabKey]);

  const titleCell = (value, index, record) => {
    return (
      <div>
        <Typography.Text strong className="nowrap ellipsis" style={{ display: 'block' }}>
          {value}
        </Typography.Text>
        <div className="nowrap text-gray">
          活动ID：{record.activityId}
          <span className={`iconfont icon-fuzhi ${styles.copy}`} onClick={() => copyText(record.activityId)} />
        </div>
      </div>
    );
  };

  // 活动主页
  const activityImgCell = (value, index, record) => {
    return (
      <div>
        {(value && (
          <img src={value} alt="" className={styles.imgCell} onClick={() => previewImg(record.name, value)} />
        )) ||
          '--'}
      </div>
    );
  };

  const activityTimeCell = (value, index, record) => {
    if (record.startTime && record.endTime) {
      return (
        <div>
          <div>起：{dayjs(record.startTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
          <div>止：{dayjs(record.endTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
        </div>
      );
    } else {
      return <div>-</div>;
    }
  };
  const createTimeCell = (value) => {
    return <div>{dayjs(value).format(constants.DATE_FORMAT_TEMPLATE)}</div>;
  };

  const formatDateTimeDayjs = (value, format = 'YYYY-MM-DD HH:mm:ss') => {
    if (value) {
      return dayjs(value).format(format);
    }
    return '';
  };

  // 跳转页面按钮点击事件
  const onJumpButtonClick = (link, searchObj: Record<string, any>) => {
    const query = QS.stringify(searchObj);
    if (link.includes('?')) {
      appHistory.push(`${link}&${query}`);
    } else {
      appHistory.push(`${link}?${query}`);
    }
  };

  getShop();

  const operateCell = (value, index, record) => {
    return (
      <div style={{ display: 'flex', columnGap: 10 }}>
        <Button type="primary" text style={{ marginRight: '10px' }} onClick={() => showDetail(index)}>
          详情
        </Button>
        {tabKey == '1' && (
          <Button
            style={{ display: 'none' }}
            type="primary"
            text
            onClick={() => {
              setTestRecord(record);
              setTestVisible(true);
            }}
          >
            数据报表
          </Button>
        )}
        {record.buttons &&
          record.buttons.map((btn) => (
            <Button
              key={btn.key}
              type={'primary'}
              text
              onClick={() =>
                onJumpButtonClick(btn.link, {
                  activityId: record.activityId,
                  activityName: encodeURIComponent(record.name),
                  startTime: formatDateTimeDayjs(record.startTime),
                  endTime: formatDateTimeDayjs(record.endTime),
                })
              }
            >
              {btn.label}
            </Button>
          ))}
        {record.attachInfos?.length > 0 && (
          <>
            <Balloon
              v2
              align="t"
              title={record.attachInfos?.length > 0 ? '请点击文件下载：' : '当前活动数据暂未上传，请联系客户运营经理~'}
              trigger={
                <Button type="primary" text disabled={record.attachInfos.length == 0}>
                  下载数据
                </Button>
              }
              triggerType="click"
              closable={false}
            >
              {record.attachInfos?.length > 0 &&
                record.attachInfos?.map((item) => (
                  <div
                    key={item.attachName}
                    className={styles.file}
                    onClick={() => {
                      window.open(item.attachUrl);
                    }}
                  >
                    <div className={item.attachName.length > 24 ? styles.text : ''}> {item.attachName}</div>
                  </div>
                ))}
            </Balloon>
          </>
        )}
      </div>
    );
  };

  return (
    <div>
      <Message type="help" className={styles.message}>
        商家可查看历史定制活动的基本信息、下载数据；若历史定制活动未在以下列表展示，或暂未支持下载数据，请联系客户运营经理~
      </Message>
      <Tab onChange={(key) => changeTabKey(key)} unmountInactiveTabs activeKey={tabKey}>
        <Tab.Item title="单店活动" key="1" />
        <Tab.Item title="联合活动" key="2" />
      </Tab>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem label="活动名称" requiredMessage="请输入活动名称">
            <Input
              value={params.activityName}
              trim
              hasClear
              maxLength={50}
              placeholder="请输入活动名称"
              onChange={(activityName) => {
                setParams({ activityName });
              }}
            />
          </FormItem>
          <FormItem name="activityId" label="活动ID" requiredMessage="请输入活动ID">
            <Input
              value={params.activityId}
              trim
              hasClear
              maxLength={50}
              placeholder="请输入活动ID"
              onChange={(activityId) => {
                setParams({ activityId });
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Form.Reset
              onClick={() => {
                handleReset();
              }}
            >
              重置
            </Form.Reset>
          </FormItem>
        </Form>
      </LzPanel>
      <LzPanel>
        <Table dataSource={list} loading={loading}>
          <Table.Column title="活动名称" dataIndex="name" cell={titleCell} />
          <Table.Column title="活动主页" dataIndex="pictureLink" cell={activityImgCell} />
          <Table.Column title="创建时间" dataIndex="createTime" cell={createTimeCell} />
          <Table.Column title="活动时间" dataIndex="startTime" cell={activityTimeCell} />
          <Table.Column
            title="活动状态"
            cell={(value, index, data) => (
              <span>
                {
                  [
                    <LzTag.Warning>未开始</LzTag.Warning>,
                    <LzTag.Success>进行中</LzTag.Success>,
                    <LzTag.Disable>已结束</LzTag.Disable>,
                  ][+data.activityStatus]
                }
              </span>
            )}
          />
          <Table.Column title="操作" cell={operateCell} />
        </Table>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={handlePage}
        />
      </LzPanel>
      <LzDialog
        style={{ width: '670px' }}
        v2
        title="活动详情"
        visible={detailVisible}
        onClose={() => setDetailVisible(false)}
        footer={[
          <Button key="confirm" type="normal" style={{ marginRight: '10px' }} onClick={() => setDetailVisible(false)}>
            确定
          </Button>,
        ]}
      >
        <div className={styles.preview}>
          <Form {...formItemLayout} isPreview>
            <FormItem label="活动名称">{detailData.name || '--'}</FormItem>
            {detailData.pictureLink && (
              <FormItem label="活动主图">
                <img src={detailData.pictureLink} alt="" className={styles.imgCell} />
              </FormItem>
            )}
            <FormItem label="活动链接">{detailData.activityUrl || '--'}</FormItem>
            <FormItem label="活动域名">{detailData.realmName || '--'}</FormItem>
            <FormItem label="活动ID">{detailData.activityId}</FormItem>
            <FormItem label="活动时间">{`${dayjs(detailData.startTime).format(constants.DATE_FORMAT_TEMPLATE)}~${dayjs(
              detailData.endTime,
            ).format(constants.DATE_FORMAT_TEMPLATE)}`}</FormItem>
            <FormItem label="活动状态">
              <span>{parsePointResListStatus(+detailData.activityStatus)}</span>
            </FormItem>
            <FormItem label="活动规则">
              <Input.TextArea value={detailData.activityRule} />
            </FormItem>
          </Form>
        </div>
      </LzDialog>
      <LzDialog
        style={{ width: '450px' }}
        v2
        title="自定义看板（内测版）"
        visible={testVisible}
        onClose={() => setTestVisible(false)}
        footer={[
          <Button
            key="confirm"
            type="primary"
            style={{ marginRight: '10px', width: 88, height: 30 }}
            onClick={() => {
              const startTime1 = dayjs(testRecord?.startTime).format('YYYY-MM-DD HH:mm:ss');
              const endTime1 = dayjs(testRecord?.endTime).format('YYYY-MM-DD HH:mm:ss');
              location.href = `https://crmbsaas.dianpusoft.cn/report/interact/data/analysis?activityId=${testRecord?.activityId}&activityType=1&activityName=${testRecord.name}&startTime=${startTime1}&endTime=${endTime1}&tab=2`;
            }}
          >
            开始使用
          </Button>,
        ]}
      >
        <p style={{ fontSize: 12 }}>
          在使用过程中，如遇到功能无法满足需求或出现异常情况，请及时反馈，以便我们进一步改进和优化。
        </p>
        <p style={{ fontSize: 14 }}>使用说明：</p>
        <p style={{ fontSize: 13 }}>1.数据由云鹿平台统计计算</p>
        <p style={{ fontSize: 13 }}>2.数据统计时间从1月2日起</p>
      </LzDialog>
    </div>
  );
};

.activitySelect {
  background: white;
  padding: 27px 20px;
  box-shadow: 0 0 15px 2px rgba(169, 182, 199, .2);
  border-radius: 5px;

  .systemTpl {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;


    .rateIcon {
      position: absolute;
      width: 42px;
      top: 17px;
      left: 0;
    }

    .tpl {
      cursor: pointer;
      height: 370px;
      width: 208px;
      overflow: hidden;
      border-radius: 5px;
      position: relative;

      &:hover {
        .mark {
          display: flex;
        }
      }

      .tplImg {
        width: 100%;
      }

      .mark {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, .7);
        display: none;
        flex-direction: column;
        align-items: center;
        border-radius: 5px;

        .markImg {
          margin-top: 60px;
          width: 158px;
          background: white;
          padding: 5px;
        }

        .markTip {
          color: #fff;
          opacity: 1;
          font-size: 14px;
          margin-top: 20px;
          font-weight: 400;
        }

        .delIcon {
          color: white;
          position: absolute;
          right: 10px;
          top: 10px;
        }

        .markBtn {
          position: absolute;
          width: 148px;
          bottom: 36px;
        }
      }

      .tag {
        position: absolute;
        top: 0;
        right: 0;
        min-width: 52px;

        .tagText {
          color: #fff;
          font-size: 12px;
          text-align: center;
          line-height: 20px;
          padding: 0 10px;
          border-bottom-left-radius: 13px;
          border-top-right-radius: 5px;
          background-image: linear-gradient(90deg, #8bcfff -21%, #8bcfff 0, #389bff 100%, #389bff 0);
        }

      }
    }

    .tplName {
      font-size: 12px;
      text-align: center;
      margin-top: 8px;
    }

    .ad {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/32153/28/17341/1030/6332b8b2Ef3a13d2c/1dfb50be5b44146c.png) no-repeat;
      background-size: 100% 100%;
      width: 208px;
      height: 370px;
      border-radius: 5px;
      padding-top: 25px;
      box-shadow: 0 0 10px 2px rgba(81, 140, 253, .22);
      box-sizing: border-box;

      .adIcon {
        width: 106px;
        height: 124px;
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/124912/1/30852/12667/633258aaE49d816ff/9549dbfca1a2529a.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
      }

      .adHeaderTitle {
        padding: 0 35px;
        font-size: 14px;
        color: #6299fe;
        margin-bottom: 10px;
        text-align: center;
      }

      .adPhone {
        font-size: 12px;
        color: #6299fe;
        text-align: center;
        margin-bottom: 20px;

        .adPhoneIcon {
          display: inline-block;
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/184059/21/27154/682/633258a9Eed8f1d07/a0c36b8e1838086d.png) 0 no-repeat;
          background-size: 14px 14px;
          padding-left: 18px;
        }
      }

      .adQrCode {
        width: 100px;
        height: 100px;
        margin: 0 auto;
        border: 1px solid lightgrey;
        cursor: pointer;
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/169912/38/31354/16698/63608f86E7130a6cf/01bfba9c47171762.png) no-repeat;
        background-size: 100% 100%;
      }

      .adFooterTitle {
        font-size: 12px;
        color: #6299fe;
        text-align: center;
        margin-top: 10px;
      }

    }
  }


}

/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-05-29 16:39
 * Description:
 */
import React, { useEffect, useState } from 'react';
import { Button, Dialog, Loading, Message, Tab } from '@alifd/next';
import styles from './style.module.scss';
import { delActivityTemplate, getActivityTemplateList } from '@/api/template';
import { getJdAppPreviewUrl, getParams } from '@/utils';
import QRCode from 'qrcode.react';
import { GetActivityTemplateResponse } from '@/api/types';
import LzEmpty from '@/components/LzEmpty';

export default (props) => {
  const { history } = props;
  const [tpl, setTpl] = useState<GetActivityTemplateResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeIndex, setActiveIndex] = useState('1');
  const activityType = getParams('activityType');
  const { venderType } = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') || '{}');
  const [showSystemTemplateTab, setShowSystemTemplateTab] = useState(true);

  const size = 148;
  const imageSettings = {
    src: require('@/assets/images/jd-logo.webp'),
    height: size / 7.4,
    width: size / 7.4,
    excavate: false,
  };
  const supplementaryCheckInActivityType = [30001, 30003, 10021];
  const handleActivityCreate = (item) => {
    if (
      props.location.state &&
      props.location.state.supDate &&
      props.location.state.taskId &&
      supplementaryCheckInActivityType.indexOf(+activityType) > -1
    ) {
      history.push(`/activity/${activityType}/${item.code}?id=${item.id}&type=tpl`, {
        supDate: props.location.state.supDate,
        taskId: props.location.state.taskId,
      });
    } else {
      history.push(`/activity/${activityType}/${item.code}?id=${item.id}&type=tpl`);
    }
  };
  const fetchTemplateList = (key) => {
    setLoading(true);
    setActiveIndex(key);
    getActivityTemplateList({
      activityType,
      scope: key,
    }).then((res) => {
      if (key == '1' && !res.length) {
        fetchTemplateList('2');
        setShowSystemTemplateTab(false);
      } else {
        setTpl(res);
      }
      setLoading(false);
    });
  };
  const delTemplate = (id: string) => {
    Dialog.confirm({
      title: '提示',
      content: '确定删除该模板吗？',
      onOk: () => {
        delActivityTemplate({ id }).then(() => {
          Message.success('删除成功');
          fetchTemplateList(activeIndex);
        });
      },
    });
  };
  useEffect(() => {
    fetchTemplateList(1);
  }, []);
  return (
    <div>
      <Tab onChange={(key) => fetchTemplateList(key)}>
        {showSystemTemplateTab && <Tab.Item title="定制模版" key={1} />}
        <Tab.Item title="我的模板" key={3} />
      </Tab>
      <Loading visible={loading} inline={false}>
        <div className={styles.activitySelect}>
          <div className="crm-label">选择活动模板</div>
          <div className={styles.systemTpl}>
            {tpl.length > 0 &&
              tpl.map((item, index) => (
                <div key={index}>
                  <div className={styles.tpl}>
                    <img className={styles.tplImg} src={item.cover} alt="" />
                    <div className={styles.mark}>
                      <div className={styles.markImg}>
                        <QRCode
                          size={size}
                          value={getJdAppPreviewUrl(
                            `/${activityType}/${item.code}/preview/?id=${item.id}&type=tpl&venderType=${venderType}`,
                          )}
                          imageSettings={imageSettings}
                        />
                      </div>
                      <span className={styles.markTip}>手机扫码预览</span>
                      <Button type="primary" className={styles.markBtn} onClick={() => handleActivityCreate(item)}>
                        应用模板
                      </Button>
                      {activeIndex === '3' && (
                        <div className={styles.delIcon} onClick={() => delTemplate(item.id as string)}>
                          <i className={`iconfont icon-shanchu`} />
                        </div>
                      )}
                    </div>
                    {item.code?.toString().startsWith('2') && (
                      <div className={styles.tag}>
                        <div className={styles.tagText}>定制</div>
                      </div>
                    )}
                    {item.code?.toString().startsWith('3') && (
                      <div className={styles.tag}>
                        <div className={styles.tagText}>自定义</div>
                      </div>
                    )}
                  </div>
                  <div className={styles.tplName}>{item.title}</div>
                </div>
              ))}
            {tpl.length <= 0 && activeIndex !== '2' && <LzEmpty />}
            {activeIndex === '2' && (
              <div className={styles.ad}>
                <div className={styles.adIcon} />
                <div className={styles.adHeaderTitle}>定制模板请联系您的陆泽运营经理</div>
                <div className={styles.adPhone}>
                  <span className={styles.adPhoneIcon}>客服电话： 15841161321</span>
                </div>
                <div className={styles.adQrCode} />
                <div className={styles.adFooterTitle}>更多问题扫码联系产品小二</div>
              </div>
            )}
          </div>
        </div>
      </Loading>
    </div>
  );
};
